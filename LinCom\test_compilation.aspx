<%@ Page Title="Test Compilation" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="test_compilation.aspx.cs" Inherits="LinCom.test_compilation" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container" style="margin-top: 50px;">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3>🔧 Test de Compilation - Module de Messagerie</h3>
                        <p>Vérification que toutes les pages se compilent correctement</p>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-info">
                            <h5>📋 Pages à Tester</h5>
                            <ul>
                                <li><strong>messagerie.aspx</strong> - Interface principale de messagerie</li>
                                <li><strong>groupes.aspx</strong> - Gestion des groupes</li>
                                <li><strong>FileUploadHandler.ashx</strong> - Handler d'upload</li>
                                <li><strong>test_fonctionnalites_avancees.aspx</strong> - Tests avancés</li>
                            </ul>
                        </div>

                        <div class="test-results">
                            <h5>🧪 Résultats des Tests</h5>
                            
                            <div class="test-item">
                                <strong>Status Compilation:</strong> 
                                <asp:Label ID="lblCompilationStatus" runat="server" Text="✅ Réussi" CssClass="text-success"></asp:Label>
                            </div>
                            
                            <div class="test-item">
                                <strong>Messagerie.aspx:</strong> 
                                <asp:Label ID="lblMessagerieStatus" runat="server" Text="En cours..." CssClass="text-warning"></asp:Label>
                            </div>
                            
                            <div class="test-item">
                                <strong>Groupes.aspx:</strong> 
                                <asp:Label ID="lblGroupesStatus" runat="server" Text="En cours..." CssClass="text-warning"></asp:Label>
                            </div>
                            
                            <div class="test-item">
                                <strong>FileUploadHandler.ashx:</strong> 
                                <asp:Label ID="lblUploadStatus" runat="server" Text="En cours..." CssClass="text-warning"></asp:Label>
                            </div>
                        </div>

                        <div class="mt-4">
                            <asp:Button ID="btnTestCompilation" runat="server" Text="🚀 Tester la Compilation" 
                                CssClass="btn btn-primary btn-lg" OnClick="btnTestCompilation_Click" />
                        </div>

                        <div class="mt-4" id="divResults" runat="server" visible="false">
                            <h5>📊 Détails des Tests</h5>
                            <asp:Literal ID="litResults" runat="server"></asp:Literal>
                        </div>

                        <div class="mt-4">
                            <h5>🔗 Liens de Test</h5>
                            <div class="btn-group" role="group">
                                <a href="messagerie.aspx" class="btn btn-outline-primary" target="_blank">Messagerie</a>
                                <a href="groupes.aspx" class="btn btn-outline-secondary" target="_blank">Groupes</a>
                                <a href="test_fonctionnalites_avancees.aspx" class="btn btn-outline-info" target="_blank">Tests Avancés</a>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .test-item {
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        .btn-group .btn {
            margin: 0 5px;
        }
    </style>

</asp:Content>
