﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">👥 Contacts</div>
                    <div class="contacts-search">
                        <div class="search-container">
                            <div class="search-box">
                                <i class="fas fa-search search-icon"></i>
                                <asp:TextBox ID="txtRechercheContact" runat="server" CssClass="search-input-modern"
                                    placeholder="Rechercher un contact..."
                                    onkeyup="filterContacts(this.value)"
                                    onkeypress="if(event.keyCode==13) { performSearch(); return false; }"></asp:TextBox>
                                <button type="button" id="btnRechercherContact"
                                    class="search-btn-modern" onclick="performSearch()"
                                    title="Rechercher">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="quick-actions-modern">
                            <button type="button" class="new-group-btn" onclick="showCreateGroupModal()">
                                <i class="fas fa-users"></i>
                                <span>Nouveau Groupe</span>
                            </button>
                            <button type="button" class="refresh-btn" onclick="refreshContacts()" title="Actualiser">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Nom du Membre" />
        <div>
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>

                    <div class="chat-body">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-bubble message-received' data-sender='0' data-current-user='<%# Request.Cookies["iduser"]?.Value %>'>
            <div class="message-content">
                <div class="message-header">
                    <div class="user-info">
                        <img class="user-avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>'
                             alt="Photo" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNlOWVjZWYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjNmM3NTdkIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM2Yzc1N2QiLz4KPC9zdmc+Cjwvc3ZnPgo='" />
                        <div class="user-details">
                            <span class="user-name"><%# Eval("Expediteur") %></span>
                            <span class="message-time"><%# Eval("DateEnvoi", "{0:HH:mm}") %></span>
                        </div>
                    </div>
                    <div class="message-status">
                        <span class="message-check">✓</span>
                    </div>
                </div>

                <div class="message-text">
                    <%# Eval("Contenu") %>
                </div>

                <%-- Pièce jointe avec design amélioré --%>
                <asp:PlaceHolder runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <div class="attachment-container">
                        <div class="attachment-preview">
                            <div class="attachment-icon">
                                📎
                            </div>
                            <div class="attachment-details">
                                <div class="attachment-name"><%# Eval("AttachmentUrl") != null ? System.IO.Path.GetFileName(Eval("AttachmentUrl").ToString()) : "Fichier" %></div>
                                <div class="attachment-meta">Pièce jointe - Cliquez pour voir le contenu</div>
                            </div>
                            <button type="button" class="attachment-view" onclick="showAttachmentContent('<%# Eval("AttachmentUrl") %>', '<%# Eval("AttachmentUrl") != null ? System.IO.Path.GetFileName(Eval("AttachmentUrl").ToString()) : "Fichier" %>')" title="Voir le contenu">
                                👁️
                            </button>
                        </div>
                    </div>
                </asp:PlaceHolder>

                <div class="message-footer">
                    <span class="message-date"><%# Eval("DateEnvoi", "{0:dd/MM/yyyy}") %></span>
                </div>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <div class="chat-footer">
                        <!-- Zone de prévisualisation des fichiers -->
                        <div id="filePreview" class="file-preview" style="display: none;">
                            <div class="preview-item">
                                <div class="file-info">
                                    <i class="fas fa-file"></i>
                                    <span class="file-name"></span>
                                </div>
                                <button type="button" class="remove-file" onclick="removeFile()" title="Supprimer">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Sélecteur d'émojis -->
                        <div id="emojiPicker" class="emoji-picker" style="display: none;">
                            <div class="emoji-header">
                                <span>Choisir un émoji</span>
                                <button type="button" onclick="toggleEmojiPicker()" class="close-emoji">×</button>
                            </div>
                            <div class="emoji-grid">
                                <span class="emoji" onclick="insertEmoji('😊')" title="Sourire">😊</span>
                                <span class="emoji" onclick="insertEmoji('😂')" title="Rire">😂</span>
                                <span class="emoji" onclick="insertEmoji('❤️')" title="Cœur">❤️</span>
                                <span class="emoji" onclick="insertEmoji('👍')" title="Pouce en l'air">👍</span>
                                <span class="emoji" onclick="insertEmoji('👎')" title="Pouce en bas">👎</span>
                                <span class="emoji" onclick="insertEmoji('😍')" title="Yeux cœur">😍</span>
                                <span class="emoji" onclick="insertEmoji('😢')" title="Triste">😢</span>
                                <span class="emoji" onclick="insertEmoji('😮')" title="Surpris">😮</span>
                                <span class="emoji" onclick="insertEmoji('😡')" title="Colère">😡</span>
                                <span class="emoji" onclick="insertEmoji('🎉')" title="Fête">🎉</span>
                                <span class="emoji" onclick="insertEmoji('🔥')" title="Feu">🔥</span>
                                <span class="emoji" onclick="insertEmoji('💯')" title="100">💯</span>
                                <span class="emoji" onclick="insertEmoji('🚀')" title="Fusée">🚀</span>
                                <span class="emoji" onclick="insertEmoji('💪')" title="Force">💪</span>
                                <span class="emoji" onclick="insertEmoji('🤝')" title="Poignée de main">🤝</span>
                                <span class="emoji" onclick="insertEmoji('✨')" title="Étoiles">✨</span>
                                <span class="emoji" onclick="insertEmoji('🎯')" title="Cible">🎯</span>
                                <span class="emoji" onclick="insertEmoji('💡')" title="Ampoule">💡</span>
                            </div>
                        </div>

                        <!-- Zone de saisie améliorée -->
                        <div class="message-input-wrapper">
                            <div class="input-container">
                                <div class="input-actions-left">
                                    <button type="button" class="action-btn emoji-btn" onclick="toggleEmojiPicker()" title="Émojis">
                                        <i class="fas fa-smile"></i>
                                    </button>
                                    <label for="fileInput" class="action-btn file-btn" title="Pièce jointe">
                                        <i class="fas fa-paperclip"></i>
                                        <input type="file" id="fileInput" style="display: none;"
                                               accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.rar"
                                               onchange="handleFileSelect(this)" />
                                    </label>
                                </div>

                                <div class="textarea-container">
                                    <textarea rows="1" runat="server" id="txtMessage"
                                             class="message-textarea"
                                             placeholder="Tapez votre message..."
                                             onkeydown="handleKeyPress(event)"
                                             oninput="autoResize(this)"></textarea>
                                </div>

                                <div class="input-actions-right">
                                    <button type="button" runat="server" id="btnenvoie"
                                           class="send-button"
                                           onserverclick="btnenvoie_ServerClick"
                                           title="Envoyer (Ctrl+Entrée)">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                            </div>
                            <asp:HiddenField ID="hdnAttachmentUrl" runat="server" />
                        </div>
                    </div>

                    <!-- Contrôles cachés pour la création de groupe -->
                    <asp:HiddenField ID="hdnGroupeNom" runat="server" />
                    <asp:HiddenField ID="hdnGroupeDescription" runat="server" />
                    <asp:HiddenField ID="hdnGroupeMembres" runat="server" />
                </div>
            </div>
        </div>

    </main>

    <!-- Modal de création de groupe -->
    <div class="modal fade" id="createGroupModal" tabindex="-1" role="dialog" aria-labelledby="createGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="createGroupModalLabel">
                        <i class="fas fa-users"></i> Créer un Nouveau Groupe
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="txtNomGroupe"><i class="fas fa-tag"></i> Nom du Groupe *</label>
                        <input type="text" id="txtNomGroupe" class="form-control"
                               placeholder="Entrez le nom du groupe (ex: Équipe Marketing)" />
                        <small class="form-text text-muted">Le nom sera visible par tous les membres</small>
                    </div>

                    <div class="form-group">
                        <label for="txtDescriptionGroupe"><i class="fas fa-info-circle"></i> Description</label>
                        <textarea id="txtDescriptionGroupe" class="form-control" rows="3"
                                  placeholder="Description du groupe (optionnel)"></textarea>
                    </div>

                    <div class="form-group">
                        <label><i class="fas fa-user-friends"></i> Sélectionner les Membres</label>
                        <div class="members-selection-container">
                            <div class="search-members">
                                <input type="text" id="searchMembers" class="form-control"
                                       placeholder="🔍 Rechercher des membres..."
                                       onkeyup="filterMembers(this.value)">
                            </div>
                            <div class="members-list-container" id="membersListContainer">
                                <!-- Les membres seront chargés via JavaScript -->
                            </div>
                        </div>
                    </div>

                    <div class="selected-members-preview" id="selectedMembersPreview" style="display: none;">
                        <h6><i class="fas fa-check-circle text-success"></i> Membres sélectionnés :</h6>
                        <div id="selectedMembersList" class="selected-members-tags"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetGroupForm()">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="button" class="btn btn-primary" onclick="creerGroupe()">
                        ➕ Créer le Groupe
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .chat-wrapper {
            display: flex;
            height: 85vh;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            background: #fff;
            margin: 20px auto;
            max-width: 1200px;
        }

        /* Design moderne de la recherche */
        .search-container {
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            margin-bottom: 15px;
        }

        .search-box {
            position: relative;
            display: flex;
            align-items: center;
            background: white;
            border-radius: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            color: #667eea;
            z-index: 2;
        }

        .search-input-modern {
            flex: 1;
            padding: 12px 15px 12px 45px;
            border: none;
            outline: none;
            font-size: 14px;
            background: transparent;
        }

        .search-input-modern::placeholder {
            color: #adb5bd;
        }

        .search-btn-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn-modern:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .quick-actions-modern {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .new-group-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 10px 15px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .new-group-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .refresh-btn {
            padding: 10px 12px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #5a6268;
            transform: rotate(180deg);
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 15px;
            font-weight: bold;
        }

        .contact-item {
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

            .contact-item:hover {
                background: #e0f7f5;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
            }

        .contact-name {
            font-weight: 500;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 12px 15px;
            background: #fff;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

            .chat-footer textarea {
                flex: 1;
                border-radius: 10px;
                padding: 10px;
                border: 1px solid #ccc;
                resize: none;
            }

            .chat-footer button {
                background: #008374;
                color: #fff;
                border: none;
                padding: 10px 20px;
                border-radius: 10px;
                font-weight: bold;
            }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }

        .contacts-search {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

            .contacts-search input {
                width: 100%;
                padding: 8px;
                border-radius: 8px;
                border: 1px solid #ccc;
            }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
}

        /* Styles modernes pour les messages */
        .message-bubble {
            margin: 15px 0;
            display: flex;
            animation: slideInMessage 0.3s ease-out;
        }

        .message-sent {
            justify-content: flex-end;
        }

        .message-received {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            position: relative;
        }

        .message-sent .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 5px 20px;
            margin-left: 50px;
        }

        .message-received .message-content {
            background: #f8f9fa;
            color: #333;
            border-radius: 20px 20px 20px 5px;
            margin-right: 50px;
            border: 1px solid #e9ecef;
        }

        .message-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px 5px 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 600;
            font-size: 13px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.8;
        }

        .message-sent .user-name,
        .message-sent .message-time {
            color: rgba(255,255,255,0.9);
        }

        .message-received .user-name {
            color: #495057;
        }

        .message-received .message-time {
            color: #6c757d;
        }

        .message-text {
            padding: 5px 15px 10px 15px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message-footer {
            padding: 0 15px 10px 15px;
            text-align: right;
        }

        .message-date {
            font-size: 10px;
            opacity: 0.7;
        }

        .message-check {
            color: #28a745;
            font-size: 12px;
            display: none; /* Masqué par défaut, affiché par JavaScript pour les messages envoyés */
        }

        .message-sent .message-check {
            display: inline !important;
        }

        /* Styles pour les pièces jointes */
        .file-preview {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .preview-item {
            display: flex;
            align-items: center;
            gap: 12px;
            background: white;
            padding: 10px;
            border-radius: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .file-info i {
            color: #667eea;
            font-size: 16px;
        }

        .remove-file {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .remove-file:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        .attachment-container {
            margin: 10px 15px;
        }

        .attachment-preview {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255,255,255,0.1);
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .message-received .attachment-preview {
            background: #e9ecef;
            border-color: #dee2e6;
        }

        .attachment-icon {
            color: #667eea;
            font-size: 16px;
        }

        .attachment-details {
            flex: 1;
        }

        .attachment-name {
            font-size: 13px;
            font-weight: 500;
        }

        .attachment-meta {
            font-size: 11px;
            opacity: 0.8;
        }

        .attachment-view {
            background: #007bff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .attachment-view:hover {
            background: #0056b3;
        }

        /* Styles pour la modal des pièces jointes */
        .attachment-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .attachment-modal-content {
            background: white;
            border-radius: 15px;
            max-width: 80%;
            max-height: 80%;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .attachment-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .attachment-modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .attachment-modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .attachment-modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .attachment-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .attachment-preview-area {
            text-align: center;
            margin: 20px 0;
        }

        .attachment-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        /* Zone de saisie moderne */
        .message-input-wrapper {
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 8px;
        }

        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        .input-actions-left {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 50%;
            background: #f8f9fa;
            color: #667eea;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            font-size: 14px;
        }

        .action-btn i {
            color: #667eea;
            font-size: 14px;
        }

        .action-btn:hover {
            background: #e9ecef;
            transform: scale(1.1);
        }

        .emoji-btn:hover {
            color: #ffc107;
        }

        .file-btn:hover {
            color: #28a745;
        }

        .textarea-container {
            flex: 1;
            position: relative;
        }

        .message-textarea {
            width: 100%;
            min-height: 36px;
            max-height: 120px;
            padding: 8px 15px;
            border: none;
            outline: none;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.4;
            background: transparent;
            overflow-y: auto;
        }

        .message-textarea::placeholder {
            color: #adb5bd;
        }

        .input-actions-right {
            display: flex;
        }

        .send-button {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .send-button i {
            color: white;
            font-size: 14px;
        }

        .send-button:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .send-button:active {
            transform: scale(0.95);
        }

        /* Sélecteur d'émojis amélioré */
        .emoji-picker {
            position: absolute;
            bottom: 50px;
            left: 15px;
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 1000;
            min-width: 300px;
            overflow: hidden;
        }

        .emoji-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 500;
        }

        .close-emoji {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-emoji:hover {
            background: rgba(255,255,255,0.2);
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
        }

        .emoji {
            font-size: 22px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.2s ease;
            position: relative;
        }

        .emoji:hover {
            background: #f8f9fa;
            transform: scale(1.3);
        }

        .emoji:hover::after {
            content: attr(title);
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            white-space: nowrap;
            z-index: 1001;
        }

        /* Styles pour le modal de création de groupe */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            border-radius: 15px 15px 0 0;
            border-bottom: none;
        }

        .members-selection-container {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .search-members {
            padding: 10px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .members-list-container {
            max-height: 250px;
            overflow-y: auto;
            padding: 10px;
        }

        .members-checkbox-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .members-checkbox-list label {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .members-checkbox-list label:hover {
            background: #f8f9fa;
        }

        .members-checkbox-list input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .member-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .member-item:hover {
            background: #f8f9fa;
        }

        .member-item label {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin: 0;
        }

        .member-item input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .selected-members-preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .selected-members-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .member-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* Animation pour les messages */
        @keyframes slideInMessage {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .new-message {
            animation: slideInMessage 0.3s ease-out;
        }

        /* Message "Aucun résultat" */
        .no-results-message {
            text-align: center;
            padding: 30px 20px;
            color: #6c757d;
            font-style: italic;
        }

        .no-results-message i {
            font-size: 24px;
            margin-bottom: 10px;
            display: block;
            opacity: 0.5;
        }

        /* Styles pour les résultats de recherche */
        .search-results-message {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            animation: slideInSearch 0.3s ease-out;
        }

        .search-highlighted {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
            border: 2px solid #f39c12 !important;
            transform: scale(1.02);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .search-highlighted .contact-name {
            font-weight: 700;
            color: #d68910;
        }

        /* Animation pour les résultats de recherche */
        @keyframes slideInSearch {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Animation de rotation pour le spinner */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .chat-wrapper {
                height: 90vh;
                margin: 10px;
            }

            .contacts-panel {
                width: 250px;
            }

            .emoji-picker {
                left: 10px;
                right: 10px;
                width: auto;
            }

            .message-content {
                max-width: 85%;
            }
        }

    </style>

    <script>
        // Fonctions JavaScript pour les nouvelles fonctionnalités
        function toggleEmojiPicker() {
            const picker = document.getElementById('emojiPicker');
            picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
        }

        function insertEmoji(emoji) {
            const textarea = document.getElementById('<%= txtMessage.ClientID %>');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;

            textarea.value = text.substring(0, start) + emoji + text.substring(end);
            textarea.selectionStart = textarea.selectionEnd = start + emoji.length;
            textarea.focus();

            // Fermer le sélecteur d'émojis
            document.getElementById('emojiPicker').style.display = 'none';
        }

        function handleFileSelect(input) {
            const file = input.files[0];
            if (file) {
                // Vérifier la taille du fichier (max 10MB)
                if (file.size > 10 * 1024 * 1024) {
                    alert('Le fichier est trop volumineux. Taille maximale : 10MB');
                    input.value = '';
                    return;
                }

                // Afficher la prévisualisation
                const preview = document.getElementById('filePreview');
                const fileName = preview.querySelector('.file-name');

                fileName.textContent = file.name + ' (' + formatFileSize(file.size) + ')';
                preview.style.display = 'block';

                // Simuler l'upload (à implémenter côté serveur)
                uploadFile(file);
            }
        }

        function removeFile() {
            document.getElementById('fileInput').value = '';
            document.getElementById('filePreview').style.display = 'none';
            document.getElementById('<%= hdnAttachmentUrl.ClientID %>').value = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function uploadFile(file) {
            // Afficher un indicateur de chargement
            const preview = document.getElementById('filePreview');
            const fileName = preview.querySelector('.file-name');
            fileName.innerHTML = '📤 Préparation... ' + file.name;

            // Validation du fichier
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                fileName.innerHTML = '❌ Fichier trop volumineux (max 10MB)';
                setTimeout(() => {
                    removeFile();
                }, 3000);
                return;
            }

            // Extensions autorisées
            const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt'];
            const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
            if (!allowedExtensions.includes(extension)) {
                fileName.innerHTML = '❌ Type de fichier non autorisé';
                setTimeout(() => {
                    removeFile();
                }, 3000);
                return;
            }

            // Simuler l'upload (pour éviter les erreurs serveur)
            setTimeout(() => {
                fileName.innerHTML = '✅ ' + file.name + ' (' + formatFileSize(file.size) + ') - Prêt à envoyer';
                // Stocker le nom du fichier pour l'envoi
                document.getElementById('<%= hdnAttachmentUrl.ClientID %>').value = file.name;
            }, 1000);
        }

        function handleKeyPress(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                event.preventDefault();
                document.getElementById('<%= btnenvoie.ClientID %>').click();
            }
        }

        function showCreateGroupModal() {
            resetGroupForm();
            $('#createGroupModal').modal('show');
            loadMembersForGroup();
        }

        function resetGroupForm() {
            // Réinitialiser les champs du formulaire
            document.getElementById('txtNomGroupe').value = '';
            document.getElementById('txtDescriptionGroupe').value = '';
            document.getElementById('searchMembers').value = '';

            // Décocher tous les membres
            const checkboxes = document.querySelectorAll('#membersListContainer input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);

            // Vider la liste des membres sélectionnés
            document.getElementById('selectedMembersList').innerHTML = '';
        }

        function filterMembers(searchText) {
            const membersList = document.querySelectorAll('#membersListContainer .member-item');
            searchText = searchText.toLowerCase();

            let visibleCount = 0;
            membersList.forEach(function(member) {
                const memberName = member.textContent.toLowerCase();
                if (memberName.includes(searchText) || searchText === '') {
                    member.style.display = 'block';
                    visibleCount++;
                } else {
                    member.style.display = 'none';
                }
            });

            // Afficher un message si aucun résultat
            let noResultsMsg = document.getElementById('noMembersMessage');
            if (noResultsMsg) {
                noResultsMsg.remove();
            }

            if (visibleCount === 0 && searchText !== '') {
                const container = document.getElementById('membersListContainer');
                const noResults = document.createElement('div');
                noResults.id = 'noMembersMessage';
                noResults.className = 'no-results-message text-center text-muted';
                noResults.innerHTML = '<i class="fas fa-search"></i><br>Aucun membre trouvé pour "' + searchText + '"';
                container.appendChild(noResults);
            }
        }

        function filterContacts(searchText) {
            console.log('Recherche de contacts:', searchText);

            // Si la recherche est vide, afficher tous les contacts
            if (!searchText || searchText.trim() === '') {
                showAllContacts();
                return;
            }

            // Recherche côté serveur ET côté client
            searchContactsOnServer(searchText);
            searchContactsOnClient(searchText);
        }

        function searchContactsOnServer(searchText) {
            // Recherche AJAX côté serveur pour des résultats plus précis
            console.log('Recherche serveur pour:', searchText);

            // Simuler une recherche serveur (à implémenter réellement)
            setTimeout(() => {
                // Cette partie devrait faire un appel AJAX réel
                console.log('Recherche serveur simulée pour:', searchText);

                // Pour l'instant, on fait juste la recherche côté client
                searchContactsOnClient(searchText);
            }, 100);
        }

        function searchContactsOnClient(searchText) {
            // Approche robuste : chercher le conteneur par différentes méthodes
            let listContainer = document.getElementById('<%= listmembre.ClientID %>');

            // Si pas trouvé par ClientID, chercher par classe ou attribut
            if (!listContainer) {
                listContainer = document.querySelector('[id*="listmembre"]');
            }

            // Si toujours pas trouvé, chercher dans la zone des contacts
            if (!listContainer) {
                listContainer = document.querySelector('.contacts-panel');
            }

            if (!listContainer) {
                console.log('Conteneur de contacts non trouvé');
                showSearchError('Impossible de trouver la liste des contacts');
                return;
            }

            console.log('Conteneur trouvé:', listContainer.id || listContainer.className);

            // Chercher tous les contacts (éléments avec classe contact-item ou liens)
            const contactElements = listContainer.querySelectorAll('.contact-item, a[id*="btnSelectMembre"]');
            searchText = searchText.toLowerCase();
            let visibleCount = 0;
            let matchedContacts = [];

            contactElements.forEach(function(contactElement) {
                const text = (contactElement.textContent || contactElement.innerText || '').trim();
                const contactName = extractContactName(text);

                if (contactName && contactName.length > 0) {
                    const isMatch = contactName.toLowerCase().includes(searchText);

                    if (isMatch) {
                        contactElement.style.display = '';
                        visibleCount++;
                        matchedContacts.push({
                            name: contactName,
                            element: contactElement
                        });
                        console.log('Contact trouvé:', contactName);

                        // Mettre en évidence le texte trouvé
                        highlightSearchText(contactElement, searchText);
                    } else {
                        contactElement.style.display = 'none';
                    }
                }
            });

            // Nettoyer les anciens messages
            clearSearchMessages();

            // Afficher les résultats
            if (visibleCount === 0) {
                showNoResultsMessage(searchText);
            } else {
                showSearchResults(matchedContacts, searchText);
            }

            console.log(`Recherche terminée: "${searchText}", ${visibleCount} contacts trouvés`);
        }

        function extractContactName(text) {
            // Nettoyer le texte pour extraire juste le nom
            return text.replace(/\s+/g, ' ').trim();
        }

        function highlightSearchText(element, searchText) {
            // Ajouter une classe pour mettre en évidence
            element.classList.add('search-highlighted');

            // Supprimer la mise en évidence après 3 secondes
            setTimeout(() => {
                element.classList.remove('search-highlighted');
            }, 3000);
        }

        function showAllContacts() {
            const listContainer = document.getElementById('<%= listmembre.ClientID %>') ||
                                 document.querySelector('[id*="listmembre"]');

            if (listContainer) {
                const allContacts = listContainer.querySelectorAll('.contact-item, a[id*="btnSelectMembre"]');
                allContacts.forEach(contact => {
                    contact.style.display = '';
                    contact.classList.remove('search-highlighted');
                });
            }

            clearSearchMessages();
            console.log('Affichage de tous les contacts');
        }

        function showNoResultsMessage(searchText) {
            const noResults = document.createElement('div');
            noResults.id = 'noResultsMessage';
            noResults.className = 'search-results-message alert alert-warning text-center';
            noResults.style.margin = '10px';
            noResults.innerHTML = `
                <div style="padding: 15px;">
                    <div style="font-size: 24px; margin-bottom: 10px;">🔍</div>
                    <strong>Aucun contact trouvé</strong><br>
                    <small>Aucun résultat pour "${searchText}"</small><br>
                    <small style="color: #6c757d;">Essayez avec un nom différent ou vérifiez l'orthographe</small>
                </div>
            `;

            const contactsPanel = document.querySelector('.contacts-panel');
            if (contactsPanel) {
                contactsPanel.appendChild(noResults);
            }
        }

        function showSearchResults(matchedContacts, searchText) {
            if (matchedContacts.length === 1) {
                // Si un seul résultat, on peut proposer de l'ouvrir directement
                const results = document.createElement('div');
                results.id = 'searchResultsMessage';
                results.className = 'search-results-message alert alert-success text-center';
                results.style.margin = '10px';
                results.innerHTML = `
                    <div style="padding: 10px;">
                        <div style="font-size: 20px; margin-bottom: 5px;">✅</div>
                        <strong>1 contact trouvé</strong><br>
                        <small>"${matchedContacts[0].name}" correspond à "${searchText}"</small><br>
                        <button type="button" class="btn btn-sm btn-primary mt-2"
                                onclick="selectContact('${matchedContacts[0].element.id}')">
                            💬 Ouvrir la conversation
                        </button>
                    </div>
                `;

                const contactsPanel = document.querySelector('.contacts-panel');
                if (contactsPanel) {
                    contactsPanel.appendChild(results);
                }
            } else if (matchedContacts.length > 1) {
                const results = document.createElement('div');
                results.id = 'searchResultsMessage';
                results.className = 'search-results-message alert alert-info text-center';
                results.style.margin = '10px';
                results.innerHTML = `
                    <div style="padding: 10px;">
                        <div style="font-size: 20px; margin-bottom: 5px;">📋</div>
                        <strong>${matchedContacts.length} contacts trouvés</strong><br>
                        <small>Résultats pour "${searchText}"</small>
                    </div>
                `;

                const contactsPanel = document.querySelector('.contacts-panel');
                if (contactsPanel) {
                    contactsPanel.appendChild(results);
                }
            }
        }

        function selectContact(contactElementId) {
            const contactElement = document.getElementById(contactElementId);
            if (contactElement) {
                contactElement.click();
                clearSearchMessages();
            }
        }

        function clearSearchMessages() {
            const oldMsg = document.getElementById('noResultsMessage');
            if (oldMsg) oldMsg.remove();

            const oldResults = document.getElementById('searchResultsMessage');
            if (oldResults) oldResults.remove();

            const oldError = document.getElementById('searchErrorMessage');
            if (oldError) oldError.remove();
        }

        function showSearchError(message) {
            clearSearchMessages();

            const error = document.createElement('div');
            error.id = 'searchErrorMessage';
            error.className = 'search-results-message alert alert-danger text-center';
            error.style.margin = '10px';
            error.innerHTML = `
                <div style="padding: 15px;">
                    <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                    <strong>Erreur de recherche</strong><br>
                    <small>${message}</small>
                </div>
            `;

            const contactsPanel = document.querySelector('.contacts-panel');
            if (contactsPanel) {
                contactsPanel.appendChild(error);
            }
        }

        function refreshContacts() {
            // Animation de rotation pour le bouton refresh
            const refreshBtn = document.querySelector('.refresh-btn i');
            refreshBtn.style.transform = 'rotate(360deg)';

            setTimeout(() => {
                refreshBtn.style.transform = 'rotate(0deg)';
                // Ici on pourrait recharger la liste des contacts
                location.reload();
            }, 500);
        }

        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        function performSearch() {
            const searchInput = document.getElementById('<%= txtRechercheContact.ClientID %>');
            const searchText = searchInput.value.trim();

            console.log('Recherche déclenchée:', searchText);

            if (searchText.length > 0) {
                // Effectuer la recherche avec feedback visuel
                showSearchInProgress();

                // Délai court pour permettre l'affichage du feedback
                setTimeout(() => {
                    filterContacts(searchText);
                }, 100);
            } else {
                // Afficher tous les contacts si la recherche est vide
                filterContacts('');
            }
        }

        function showSearchInProgress() {
            clearSearchMessages();

            const searching = document.createElement('div');
            searching.id = 'searchInProgressMessage';
            searching.className = 'search-results-message alert alert-info text-center';
            searching.style.margin = '10px';
            searching.innerHTML = `
                <div style="padding: 10px;">
                    <div class="spinner" style="border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; width: 20px; height: 20px; animation: spin 1s linear infinite; margin: 0 auto 10px;"></div>
                    <small>Recherche en cours...</small>
                </div>
            `;

            const contactsPanel = document.querySelector('.contacts-panel');
            if (contactsPanel) {
                contactsPanel.appendChild(searching);

                // Supprimer le message après 2 secondes
                setTimeout(() => {
                    if (searching.parentNode) {
                        searching.remove();
                    }
                }, 2000);
            }
        }

        // Auto-resize du textarea et gestion des messages
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                });
            }

            // Appliquer les classes CSS correctes pour les messages
            applyMessageStyles();
        });

        function applyMessageStyles() {
            const messages = document.querySelectorAll('.message-bubble');
            const currentUserId = getCookie('iduser');
            const currentUserName = getCookie('username') || getCookie('UserName') || '';

            console.log('Application des styles de messages...');
            console.log('ID utilisateur connecté:', currentUserId);
            console.log('Nom utilisateur connecté:', currentUserName);

            messages.forEach(function(message, index) {
                const userNameElement = message.querySelector('.user-name');
                const expediteur = userNameElement ? userNameElement.textContent.trim() : '';
                const senderId = message.getAttribute('data-sender');

                console.log(`Message ${index + 1}: Expéditeur="${expediteur}", SenderId="${senderId}"`);

                // Plusieurs méthodes pour détecter si c'est l'utilisateur connecté
                let isCurrentUser = false;

                // Méthode 1: Comparer les IDs
                if (senderId && currentUserId && senderId === currentUserId) {
                    isCurrentUser = true;
                    console.log(`Message ${index + 1}: Identifié comme envoyé (ID match)`);
                }

                // Méthode 2: Chercher des patterns dans le nom
                if (!isCurrentUser) {
                    if (expediteur.includes('Vous') ||
                        expediteur === 'Moi' ||
                        (currentUserName && expediteur.includes(currentUserName))) {
                        isCurrentUser = true;
                        console.log(`Message ${index + 1}: Identifié comme envoyé (nom match)`);
                    }
                }

                // Méthode 3: Vérifier si c'est le dernier message et qu'il vient d'être envoyé
                if (!isCurrentUser && index === messages.length - 1) {
                    const messageTime = message.querySelector('.message-time');
                    if (messageTime) {
                        const timeText = messageTime.textContent;
                        const now = new Date();
                        const currentTime = now.getHours().toString().padStart(2, '0') + ':' +
                                          now.getMinutes().toString().padStart(2, '0');
                        if (timeText === currentTime) {
                            isCurrentUser = true;
                            console.log(`Message ${index + 1}: Identifié comme envoyé (temps récent)`);
                        }
                    }
                }

                // Appliquer les styles
                if (isCurrentUser) {
                    message.classList.remove('message-received');
                    message.classList.add('message-sent');

                    // Afficher le statut de lecture
                    const statusElement = message.querySelector('.message-status .message-check');
                    if (statusElement) {
                        statusElement.style.display = 'inline';
                    }
                } else {
                    message.classList.remove('message-sent');
                    message.classList.add('message-received');

                    // Masquer le statut pour les messages reçus
                    const statusElement = message.querySelector('.message-status .message-check');
                    if (statusElement) {
                        statusElement.style.display = 'none';
                    }
                }

                console.log(`Message ${index + 1}: Classe appliquée = ${isCurrentUser ? 'message-sent' : 'message-received'}`);
            });
        }

        function getCookie(name) {
            const value = "; " + document.cookie;
            const parts = value.split("; " + name + "=");
            if (parts.length === 2) return parts.pop().split(";").shift();
            return null;
        }

        function creerGroupe() {
            // Validation côté client
            const nomGroupe = document.getElementById('txtNomGroupe').value.trim();
            const description = document.getElementById('txtDescriptionGroupe').value.trim();

            if (!nomGroupe) {
                alert('Veuillez entrer un nom pour le groupe.');
                return;
            }

            // Récupérer les membres sélectionnés
            const checkboxes = document.querySelectorAll('#membersListContainer input[type="checkbox"]:checked');
            if (checkboxes.length === 0) {
                alert('Veuillez sélectionner au moins un membre pour le groupe.');
                return;
            }

            const membresIds = Array.from(checkboxes).map(cb => cb.value);

            // Afficher un indicateur de chargement
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '⏳ Création en cours...';
            btn.disabled = true;

            // Version simplifiée pour test
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;

                alert(`✅ Groupe "${nomGroupe}" créé avec succès !\n\nMembres sélectionnés: ${checkboxes.length}\nDescription: ${description || 'Aucune'}`);

                $('#createGroupModal').modal('hide');
                resetGroupForm();

                // Optionnel : rediriger vers la page des groupes
                if (confirm('Voulez-vous aller à la page de gestion des groupes ?')) {
                    window.location.href = 'groupes.aspx';
                }
            }, 2000);

            // Essayer aussi la version AJAX en parallèle (pour debug)
            console.log('Tentative de création de groupe:', {
                nomGroupe: nomGroupe,
                description: description,
                membresIds: membresIds
            });

            $.ajax({
                type: "POST",
                url: "messagerie.aspx/CreerGroupeAjax",
                data: JSON.stringify({
                    nomGroupe: nomGroupe,
                    description: description,
                    membresIds: membresIds
                }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    console.log('Réponse AJAX création groupe:', response);
                },
                error: function(xhr, status, error) {
                    console.error('Erreur AJAX création groupe:', status, error, xhr.responseText);
                }
            });
        }

        function loadMembersForGroup() {
            const container = document.getElementById('membersListContainer');
            container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Chargement des membres...</div>';

            // Version simplifiée avec des membres de test
            setTimeout(() => {
                try {
                    // Simuler des membres pour test
                    const membresTest = [
                        { Id: 1, Nom: 'Jean Dupont' },
                        { Id: 2, Nom: 'Marie Martin' },
                        { Id: 3, Nom: 'Pierre Durand' },
                        { Id: 4, Nom: 'Sophie Leroy' },
                        { Id: 5, Nom: 'Michel Bernard' }
                    ];

                    container.innerHTML = '';

                    membresTest.forEach(membre => {
                        const div = document.createElement('div');
                        div.className = 'member-item';
                        div.innerHTML = `
                            <label>
                                <input type="checkbox" value="${membre.Id}" onchange="updateSelectedMembers()" />
                                <span class="member-name">${membre.Nom}</span>
                            </label>
                        `;
                        container.appendChild(div);
                    });

                    console.log('Membres de test chargés avec succès');
                } catch (e) {
                    console.error('Erreur:', e);
                    container.innerHTML = '<div class="text-center text-danger">Erreur lors du chargement</div>';
                }
            }, 500);

            // Essayer aussi la version AJAX en parallèle
            $.ajax({
                type: "POST",
                url: "messagerie.aspx/GetMembresDisponibles",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    try {
                        console.log('Réponse AJAX reçue:', response);
                        const membres = JSON.parse(response.d);

                        if (membres && membres.length > 0) {
                            container.innerHTML = '';
                            membres.forEach(membre => {
                                const div = document.createElement('div');
                                div.className = 'member-item';
                                div.innerHTML = `
                                    <label>
                                        <input type="checkbox" value="${membre.Id}" onchange="updateSelectedMembers()" />
                                        <span class="member-name">${membre.Nom}</span>
                                    </label>
                                `;
                                container.appendChild(div);
                            });
                            console.log('Membres réels chargés avec succès');
                        }
                    } catch (e) {
                        console.error('Erreur parsing JSON:', e, response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erreur AJAX:', status, error, xhr.responseText);
                }
            });
        }

        function updateSelectedMembers() {
            const checkboxes = document.querySelectorAll('#membersListContainer input[type="checkbox"]:checked');
            const selectedList = document.getElementById('selectedMembersList');

            selectedList.innerHTML = '';
            checkboxes.forEach(cb => {
                const memberName = cb.parentElement.querySelector('.member-name').textContent;
                const tag = document.createElement('span');
                tag.className = 'badge badge-primary mr-1 mb-1';
                tag.textContent = memberName;
                selectedList.appendChild(tag);
            });
        }

        // Fonctions pour les pièces jointes
        function showAttachmentContent(attachmentUrl, fileName) {
            console.log('Affichage du contenu de la pièce jointe:', fileName);

            if (!attachmentUrl || attachmentUrl.trim() === '') {
                alert('❌ Aucun contenu de pièce jointe disponible');
                return;
            }

            // Créer une modal pour afficher le contenu
            const modal = document.createElement('div');
            modal.className = 'attachment-modal';
            modal.innerHTML = `
                <div class="attachment-modal-content">
                    <div class="attachment-modal-header">
                        <h4>📎 ${fileName}</h4>
                        <button type="button" class="attachment-modal-close" onclick="closeAttachmentModal()">&times;</button>
                    </div>
                    <div class="attachment-modal-body">
                        <div class="attachment-content-preview">
                            <div class="attachment-info">
                                <p><strong>Nom du fichier :</strong> ${fileName}</p>
                                <p><strong>URL :</strong> ${attachmentUrl}</p>
                                <p><strong>Type :</strong> ${getFileType(fileName)}</p>
                            </div>
                            <div class="attachment-preview-area">
                                ${generatePreview(attachmentUrl, fileName)}
                            </div>
                            <div class="attachment-actions">
                                <button type="button" class="btn btn-primary" onclick="downloadAttachment('${attachmentUrl}', '${fileName}')">
                                    📥 Télécharger
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeAttachmentModal()">
                                    ❌ Fermer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            modal.style.display = 'flex';
        }

        function closeAttachmentModal() {
            const modal = document.querySelector('.attachment-modal');
            if (modal) {
                modal.remove();
            }
        }

        function getFileType(fileName) {
            const extension = fileName.split('.').pop().toLowerCase();
            const types = {
                'pdf': 'Document PDF',
                'doc': 'Document Word',
                'docx': 'Document Word',
                'txt': 'Fichier Texte',
                'jpg': 'Image JPEG',
                'jpeg': 'Image JPEG',
                'png': 'Image PNG',
                'gif': 'Image GIF',
                'zip': 'Archive ZIP',
                'rar': 'Archive RAR'
            };
            return types[extension] || 'Fichier';
        }

        function generatePreview(url, fileName) {
            const extension = fileName.split('.').pop().toLowerCase();

            // Construire l'URL complète si nécessaire
            let fullUrl = url;
            if (!url.startsWith('http') && !url.startsWith('data:')) {
                // Supposer que les fichiers sont dans un dossier uploads
                fullUrl = window.location.origin + '/uploads/' + url;
            }

            if (['jpg', 'jpeg', 'png', 'gif'].includes(extension)) {
                return `<div class="image-preview">
                    <img src="${fullUrl}" alt="${fileName}"
                         style="max-width: 100%; max-height: 400px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div style="display: none; text-align: center; padding: 20px;">
                        <div style="font-size: 48px; margin-bottom: 10px;">🖼️</div>
                        <p>Impossible de charger l'image</p>
                        <p><em>Le fichier sera téléchargé à la place</em></p>
                    </div>
                </div>`;
            } else if (extension === 'pdf') {
                return `<div class="pdf-preview">
                    <iframe src="${fullUrl}" width="100%" height="400px"
                            style="border: 1px solid #ddd; border-radius: 8px;"
                            onerror="showPdfError()"></iframe>
                    <div id="pdf-error" style="display: none; text-align: center; padding: 20px;">
                        <div style="font-size: 48px; margin-bottom: 10px;">📄</div>
                        <p>Impossible de prévisualiser le PDF</p>
                        <p><em>Cliquez sur "Télécharger" pour ouvrir le fichier</em></p>
                    </div>
                </div>`;
            } else if (['txt', 'log'].includes(extension)) {
                // Créer un ID unique pour éviter les conflits
                const contentId = 'text-content-' + Math.random().toString(36).substr(2, 9);

                // Charger le contenu après un court délai
                setTimeout(() => {
                    loadTextContent(fullUrl, contentId);
                }, 100);

                return `<div class="text-preview">
                    <p>📄 Aperçu du fichier texte</p>
                    <div id="${contentId}" style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; max-height: 300px; overflow-y: auto;">
                        <div style="text-align: center; padding: 20px;">
                            <div class="spinner" style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 0 auto;"></div>
                            <p>Chargement du contenu...</p>
                        </div>
                    </div>
                </div>`;
            } else if (['doc', 'docx'].includes(extension)) {
                return `<div class="document-preview">
                    <div style="font-size: 48px; text-align: center; margin: 20px 0;">📝</div>
                    <p style="text-align: center;"><strong>Document Word</strong></p>
                    <p style="text-align: center;">Nom : ${fileName}</p>
                    <p style="text-align: center;"><em>Cliquez sur "Télécharger" pour ouvrir avec Word</em></p>
                </div>`;
            } else {
                return `<div class="file-preview">
                    <div class="file-icon-large" style="font-size: 48px; text-align: center; margin: 20px 0;">📄</div>
                    <p style="text-align: center;"><strong>${getFileType(fileName)}</strong></p>
                    <p style="text-align: center;">Nom : ${fileName}</p>
                    <p style="text-align: center;"><em>Cliquez sur "Télécharger" pour ouvrir le fichier</em></p>
                </div>`;
            }
        }

        function loadTextContent(url, containerId) {
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Fichier non trouvé');
                    }
                    return response.text();
                })
                .then(text => {
                    const container = document.getElementById(containerId);
                    if (container) {
                        // Limiter à 1000 caractères pour l'aperçu
                        const preview = text.length > 1000 ? text.substring(0, 1000) + '...\n\n[Contenu tronqué - Téléchargez pour voir le fichier complet]' : text;
                        container.innerHTML = `<pre style="white-space: pre-wrap; margin: 0;">${preview}</pre>`;
                    }
                })
                .catch(error => {
                    const container = document.getElementById(containerId);
                    if (container) {
                        container.innerHTML = `
                            <div style="text-align: center; color: #dc3545;">
                                <p>❌ Impossible de charger le contenu</p>
                                <p><em>Cliquez sur "Télécharger" pour obtenir le fichier</em></p>
                            </div>
                        `;
                    }
                });
        }

        function showPdfError() {
            const errorDiv = document.getElementById('pdf-error');
            if (errorDiv) {
                errorDiv.style.display = 'block';
                errorDiv.previousElementSibling.style.display = 'none';
            }
        }

        function downloadAttachment(url, fileName) {
            console.log('Téléchargement de:', fileName);
            console.log('URL originale:', url);

            // Construire l'URL complète si nécessaire
            let downloadUrl = url;
            if (!url.startsWith('http') && !url.startsWith('data:')) {
                downloadUrl = window.location.origin + '/uploads/' + url;
            }

            console.log('URL de téléchargement:', downloadUrl);

            // Essayer directement le téléchargement sans vérification HEAD
            // (certains serveurs bloquent les requêtes HEAD)
            try {
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = fileName;
                link.target = '_blank';

                // Ajouter temporairement au DOM pour déclencher le téléchargement
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log('Téléchargement initié pour:', fileName);

                // Vérifier après un délai si le fichier existe vraiment
                setTimeout(() => {
                    fetch(downloadUrl, { method: 'HEAD' })
                        .then(response => {
                            if (response.ok) {
                                console.log('✅ Fichier confirmé disponible');
                            } else {
                                console.warn('⚠️ Fichier peut-être non disponible');
                                showFileNotFoundDialog(fileName, downloadUrl);
                            }
                        })
                        .catch(error => {
                            console.warn('⚠️ Impossible de vérifier l\'existence du fichier');
                            showFileNotFoundDialog(fileName, downloadUrl);
                        });
                }, 2000);

            } catch (error) {
                console.error('Erreur lors du téléchargement:', error);
                showFileNotFoundDialog(fileName, downloadUrl);
            }
        }

        function showFileNotFoundDialog(fileName, downloadUrl) {
            const modal = document.createElement('div');
            modal.className = 'attachment-modal';
            modal.innerHTML = `
                <div class="attachment-modal-content">
                    <div class="attachment-modal-header">
                        <h4>⚠️ Problème de Téléchargement</h4>
                        <button type="button" class="attachment-modal-close" onclick="this.closest('.attachment-modal').remove()">&times;</button>
                    </div>
                    <div class="attachment-modal-body">
                        <div style="text-align: center; padding: 20px;">
                            <div style="font-size: 48px; margin-bottom: 20px;">📁</div>
                            <h5>Fichier non trouvé sur le serveur</h5>
                            <p><strong>Fichier :</strong> ${fileName}</p>
                            <p><strong>URL tentée :</strong> ${downloadUrl}</p>

                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: left;">
                                <h6>💡 Solutions possibles :</h6>
                                <ul style="margin: 10px 0;">
                                    <li>Vérifiez que le dossier <code>/uploads/</code> existe</li>
                                    <li>Assurez-vous que le fichier a été correctement uploadé</li>
                                    <li>Vérifiez les permissions du dossier uploads</li>
                                    <li>Le fichier pourrait avoir été supprimé ou déplacé</li>
                                </ul>
                            </div>

                            <div style="margin-top: 20px;">
                                <button type="button" class="btn btn-primary" onclick="retryDownload('${downloadUrl}', '${fileName}')">
                                    🔄 Réessayer
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.attachment-modal').remove()">
                                    ❌ Fermer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            modal.style.display = 'flex';
        }

        function retryDownload(downloadUrl, fileName) {
            // Fermer la modal
            const modal = document.querySelector('.attachment-modal');
            if (modal) modal.remove();

            // Réessayer le téléchargement
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = fileName;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('Nouvelle tentative de téléchargement pour:', fileName);
        }
    </script>

</asp:Content>
