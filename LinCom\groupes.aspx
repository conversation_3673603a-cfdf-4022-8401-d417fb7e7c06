<%@ Page Title="Gestion des Groupes" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="groupes.aspx.cs" Inherits="LinCom.groupes" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container-fluid" style="margin-top: 30px;">
        <div class="row">
            <div class="col-lg-12">
                <div class="groups-header">
                    <h2><i class="fas fa-users"></i> Gestion des Groupes de Discussion</h2>
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#createGroupModal">
                        <i class="fas fa-plus"></i> Créer un Groupe
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Liste des groupes -->
            <div class="col-lg-8">
                <div class="groups-list">
                    <asp:Repeater ID="rptGroupes" runat="server" OnItemCommand="rptGroupes_ItemCommand">
                        <ItemTemplate>
                            <div class="group-card">
                                <div class="group-info">
                                    <div class="group-avatar">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="group-details">
                                        <h4><%# Eval("Sujet") %></h4>
                                        <p class="group-meta">
                                            <span><i class="fas fa-user"></i> <%# Eval("NombreParticipants") %> membres</span>
                                            <span><i class="fas fa-calendar"></i> Créé le <%# Eval("CreatedAt", "{0:dd/MM/yyyy}") %></span>
                                        </p>
                                        <p class="group-description"><%# Eval("Description") %></p>
                                    </div>
                                </div>
                                <div class="group-actions">
                                    <asp:LinkButton ID="btnVoirGroupe" runat="server" 
                                        CommandName="voir" CommandArgument='<%# Eval("ConversationId") %>'
                                        CssClass="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> Voir
                                    </asp:LinkButton>
                                    <asp:LinkButton ID="btnModifierGroupe" runat="server" 
                                        CommandName="modifier" CommandArgument='<%# Eval("ConversationId") %>'
                                        CssClass="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-edit"></i> Modifier
                                    </asp:LinkButton>
                                    <asp:LinkButton ID="btnSupprimerGroupe" runat="server" 
                                        CommandName="supprimer" CommandArgument='<%# Eval("ConversationId") %>'
                                        CssClass="btn btn-outline-danger btn-sm"
                                        OnClientClick="return confirm('Êtes-vous sûr de vouloir supprimer ce groupe ?');">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </asp:LinkButton>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:Repeater>
                </div>
            </div>

            <!-- Panneau latéral -->
            <div class="col-lg-4">
                <div class="side-panel">
                    <div class="stats-card">
                        <h5><i class="fas fa-chart-bar"></i> Statistiques</h5>
                        <div class="stat-item">
                            <span class="stat-label">Total Groupes</span>
                            <span class="stat-value"><asp:Literal ID="litTotalGroupes" runat="server">0</asp:Literal></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Groupes Actifs</span>
                            <span class="stat-value"><asp:Literal ID="litGroupesActifs" runat="server">0</asp:Literal></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Mes Groupes</span>
                            <span class="stat-value"><asp:Literal ID="litMesGroupes" runat="server">0</asp:Literal></span>
                        </div>
                    </div>

                    <div class="recent-activity">
                        <h5><i class="fas fa-clock"></i> Activité Récente</h5>
                        <asp:Repeater ID="rptActiviteRecente" runat="server">
                            <ItemTemplate>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-comment"></i>
                                    </div>
                                    <div class="activity-content">
                                        <p><strong><%# Eval("Expediteur") %></strong> a envoyé un message dans <strong><%# Eval("Groupe") %></strong></p>
                                        <small><%# Eval("DateEnvoi", "{0:dd/MM/yyyy HH:mm}") %></small>
                                    </div>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de création de groupe -->
    <div class="modal fade" id="createGroupModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-users"></i> Créer un Nouveau Groupe</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="txtNomGroupe">Nom du Groupe *</label>
                        <asp:TextBox ID="txtNomGroupe" runat="server" CssClass="form-control" 
                            placeholder="Entrez le nom du groupe"></asp:TextBox>
                    </div>
                    <div class="form-group">
                        <label for="txtDescriptionGroupe">Description</label>
                        <asp:TextBox ID="txtDescriptionGroupe" runat="server" CssClass="form-control" 
                            TextMode="MultiLine" Rows="3" 
                            placeholder="Description du groupe (optionnel)"></asp:TextBox>
                    </div>
                    <div class="form-group">
                        <label>Sélectionner les Membres</label>
                        <div class="members-selection">
                            <asp:CheckBoxList ID="cblMembres" runat="server" CssClass="members-list">
                            </asp:CheckBoxList>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                    <asp:Button ID="btnCreerGroupe" runat="server" Text="Créer le Groupe" 
                        CssClass="btn btn-primary" OnClick="btnCreerGroupe_Click" />
                </div>
            </div>
        </div>
    </div>

    <style>
        .groups-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }

        .groups-list {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .group-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .group-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .group-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .group-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-right: 15px;
        }

        .group-details h4 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .group-meta {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }

        .group-meta span {
            margin-right: 15px;
        }

        .group-description {
            margin: 5px 0 0 0;
            color: #888;
            font-size: 13px;
        }

        .group-actions {
            display: flex;
            gap: 10px;
        }

        .side-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .stats-card, .recent-activity {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-value {
            font-weight: bold;
            color: #667eea;
            font-size: 18px;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 30px;
            height: 30px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            color: #667eea;
        }

        .activity-content p {
            margin: 0;
            font-size: 14px;
        }

        .activity-content small {
            color: #888;
        }

        .members-selection {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
        }

        .members-list label {
            display: block;
            padding: 5px 0;
            cursor: pointer;
        }

        .members-list input[type="checkbox"] {
            margin-right: 10px;
        }

        @media (max-width: 768px) {
            .groups-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .group-card {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .group-actions {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

</asp:Content>
