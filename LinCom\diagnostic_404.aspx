<%@ Page Title="Diagnostic 404" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="diagnostic_404.aspx.cs" Inherits="LinCom.diagnostic_404" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container" style="margin-top: 50px;">
        <div class="row">
            <div class="col-lg-12">
                <div class="alert alert-warning text-center">
                    <h2>🔧 Diagnostic d'Erreur 404</h2>
                    <p>Analysons pourquoi vous obtenez une erreur 404</p>
                    
                    <div class="mt-4">
                        <h4>📋 Informations de Diagnostic :</h4>
                        <div class="diagnostic-info">
                            <div class="info-item">
                                <strong>URL Demandée :</strong>
                                <span id="currentUrl"></span>
                            </div>
                            <div class="info-item">
                                <strong>Serveur :</strong>
                                <span><%= Request.Url.Host %></span>
                            </div>
                            <div class="info-item">
                                <strong>Port :</strong>
                                <span><%= Request.Url.Port %></span>
                            </div>
                            <div class="info-item">
                                <strong>Chemin de l'Application :</strong>
                                <span><%= Request.ApplicationPath %></span>
                            </div>
                            <div class="info-item">
                                <strong>Répertoire Physique :</strong>
                                <span><%= Server.MapPath("~") %></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4>🧪 Tests de Connectivité :</h4>
                        <div class="connectivity-tests">
                            <div class="test-item">
                                <button class="btn btn-primary" onclick="testPage('home.aspx')">
                                    🏠 Tester home.aspx
                                </button>
                                <span id="test-home" class="test-result"></span>
                            </div>
                            <div class="test-item">
                                <button class="btn btn-primary" onclick="testPage('messagerie.aspx')">
                                    💬 Tester messagerie.aspx
                                </button>
                                <span id="test-messagerie" class="test-result"></span>
                            </div>
                            <div class="test-item">
                                <button class="btn btn-primary" onclick="testPage('login.aspx')">
                                    🔐 Tester login.aspx
                                </button>
                                <span id="test-login" class="test-result"></span>
                            </div>
                            <div class="test-item">
                                <button class="btn btn-primary" onclick="testPage('Default.aspx')">
                                    📄 Tester Default.aspx
                                </button>
                                <span id="test-default" class="test-result"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4>🔍 Causes Possibles de l'Erreur 404 :</h4>
                        <div class="causes-list">
                            <div class="cause-item">
                                <span class="cause-icon">1️⃣</span>
                                <strong>Serveur Web non démarré</strong>
                                <p>IIS Express ou le serveur de développement n'est pas en cours d'exécution</p>
                                <div class="solution">
                                    <strong>Solution :</strong> Démarrez le projet depuis Visual Studio (F5 ou Ctrl+F5)
                                </div>
                            </div>
                            <div class="cause-item">
                                <span class="cause-icon">2️⃣</span>
                                <strong>Port incorrect</strong>
                                <p>L'URL utilise un port différent de celui configuré</p>
                                <div class="solution">
                                    <strong>Solution :</strong> Vérifiez le port dans les propriétés du projet
                                </div>
                            </div>
                            <div class="cause-item">
                                <span class="cause-icon">3️⃣</span>
                                <strong>Fichier manquant</strong>
                                <p>Le fichier .aspx demandé n'existe pas ou n'est pas compilé</p>
                                <div class="solution">
                                    <strong>Solution :</strong> Recompilez le projet (Build → Rebuild Solution)
                                </div>
                            </div>
                            <div class="cause-item">
                                <span class="cause-icon">4️⃣</span>
                                <strong>Configuration Web.config</strong>
                                <p>Erreur dans la configuration du routage ou des handlers</p>
                                <div class="solution">
                                    <strong>Solution :</strong> Vérifiez le fichier Web.config
                                </div>
                            </div>
                            <div class="cause-item">
                                <span class="cause-icon">5️⃣</span>
                                <strong>Erreur de compilation</strong>
                                <p>Le projet ne compile pas correctement</p>
                                <div class="solution">
                                    <strong>Solution :</strong> Vérifiez la fenêtre "Error List" dans Visual Studio
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4>🚀 Actions Recommandées :</h4>
                        <div class="actions-list">
                            <div class="action-step">
                                <span class="step-number">1</span>
                                <strong>Vérifier Visual Studio</strong>
                                <p>Assurez-vous que Visual Studio est ouvert avec le projet LinCom chargé</p>
                            </div>
                            <div class="action-step">
                                <span class="step-number">2</span>
                                <strong>Recompiler le Projet</strong>
                                <p>Build → Rebuild Solution (Ctrl+Shift+B)</p>
                            </div>
                            <div class="action-step">
                                <span class="step-number">3</span>
                                <strong>Démarrer le Serveur</strong>
                                <p>Debug → Start Debugging (F5) ou Start Without Debugging (Ctrl+F5)</p>
                            </div>
                            <div class="action-step">
                                <span class="step-number">4</span>
                                <strong>Vérifier l'URL</strong>
                                <p>Utilisez l'URL générée automatiquement par Visual Studio</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4>📞 URLs de Test Directes :</h4>
                        <div class="test-urls">
                            <div class="url-item">
                                <a href="home.aspx" target="_blank" class="btn btn-success">
                                    🏠 Aller à home.aspx
                                </a>
                            </div>
                            <div class="url-item">
                                <a href="messagerie.aspx" target="_blank" class="btn btn-info">
                                    💬 Aller à messagerie.aspx
                                </a>
                            </div>
                            <div class="url-item">
                                <a href="login.aspx" target="_blank" class="btn btn-warning">
                                    🔐 Aller à login.aspx
                                </a>
                            </div>
                            <div class="url-item">
                                <a href="Default.aspx" target="_blank" class="btn btn-secondary">
                                    📄 Aller à Default.aspx
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="alert alert-info">
                            <h5>💡 Conseil :</h5>
                            <p>Si cette page s'affiche correctement, cela signifie que le serveur fonctionne. 
                            Le problème vient probablement d'une URL incorrecte ou d'un fichier spécifique manquant.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .diagnostic-info {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .connectivity-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .test-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .test-result {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }

        .causes-list {
            text-align: left;
            max-width: 800px;
            margin: 0 auto;
        }

        .cause-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }

        .cause-icon {
            font-size: 24px;
            margin-right: 10px;
        }

        .solution {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #28a745;
        }

        .actions-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .action-step {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-top: 4px solid #007bff;
        }

        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .test-urls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
        }

        .url-item {
            margin: 5px;
        }

        @media (max-width: 768px) {
            .connectivity-tests {
                grid-template-columns: 1fr;
            }
            
            .actions-list {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <script>
        // Afficher l'URL actuelle
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('currentUrl').textContent = window.location.href;
        });

        function testPage(pageName) {
            const resultElement = document.getElementById('test-' + pageName.replace('.aspx', ''));
            resultElement.textContent = '⏳ Test en cours...';
            resultElement.style.color = '#ffc107';

            fetch(pageName, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        resultElement.textContent = '✅ Accessible';
                        resultElement.style.color = '#28a745';
                    } else {
                        resultElement.textContent = `❌ Erreur ${response.status}`;
                        resultElement.style.color = '#dc3545';
                    }
                })
                .catch(error => {
                    resultElement.textContent = '❌ Erreur de connexion';
                    resultElement.style.color = '#dc3545';
                    console.error('Erreur:', error);
                });
        }

        // Test automatique au chargement
        setTimeout(() => {
            ['home.aspx', 'messagerie.aspx', 'login.aspx', 'Default.aspx'].forEach(page => {
                testPage(page);
            });
        }, 1000);
    </script>
</asp:Content>
