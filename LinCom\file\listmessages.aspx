<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="listmessages.aspx.cs" Inherits="LinCom.file.listmessages" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Breadcomb area Start-->
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">
                                    <div class="breadcomb-icon">
                                        <i class="notika-icon notika-mail"></i>
                                    </div>
                                    <div class="breadcomb-ctn">
                                        <h2>Gestion des Messages</h2>
                                        <p>Administration des messages et conversations</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-report">
                                    <button data-toggle="tooltip" data-placement="left" title="Actualiser" class="btn" onclick="location.reload();">
                                        <i class="notika-icon notika-refresh"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->

    <!-- Data Table area Start-->
    <div class="data-table-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="data-table-list">
                        <div class="basic-tb-hd">
                            <h2>Liste des Messages</h2>
                            <p>Gérer et modérer les messages de la plateforme</p>
                        </div>
                        
                        <!-- Filtres -->
                        <div class="row" style="margin-bottom: 20px;">
                            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                                <div class="form-group">
                                    <label>Rechercher par contenu :</label>
                                    <asp:TextBox ID="txtRecherche" runat="server" CssClass="form-control" placeholder="Rechercher dans les messages..."></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                                <div class="form-group">
                                    <label>Filtrer par expéditeur :</label>
                                    <asp:DropDownList ID="ddlExpediteur" runat="server" CssClass="form-control">
                                        <asp:ListItem Value="">Tous les expéditeurs</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                                <div class="form-group">
                                    <label>Date :</label>
                                    <asp:TextBox ID="txtDateFiltre" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-6 col-xs-12">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <asp:Button ID="btnFiltrer" runat="server" Text="Filtrer" CssClass="btn btn-primary btn-block" OnClick="btnFiltrer_Click" />
                                </div>
                            </div>
                        </div>

                        <!-- Messages de statut -->
                        <div id="div_msg_success" runat="server" visible="false" class="alert alert-success">
                            <strong>Succès!</strong> <asp:Literal ID="litSuccessMessage" runat="server"></asp:Literal>
                        </div>
                        <div id="div_msg_error" runat="server" visible="false" class="alert alert-danger">
                            <strong>Erreur!</strong> <asp:Literal ID="litErrorMessage" runat="server"></asp:Literal>
                        </div>

                        <!-- Tableau des messages -->
                        <div class="table-responsive">
                            <asp:GridView ID="gdvmessages" runat="server" AutoGenerateColumns="false" 
                                CssClass="table table-striped table-hover" OnRowCommand="gdvmessages_RowCommand"
                                AllowPaging="true" PageSize="20" OnPageIndexChanging="gdvmessages_PageIndexChanging">
                                <Columns>
                                    <asp:BoundField DataField="id" HeaderText="ID" ItemStyle-Width="50px" />
                                    <asp:BoundField DataField="Expediteur" HeaderText="Expéditeur" ItemStyle-Width="150px" />
                                    <asp:TemplateField HeaderText="Message" ItemStyle-Width="300px">
                                        <ItemTemplate>
                                            <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                <%# Eval("Contenu") %>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:BoundField DataField="DateEnvoi" HeaderText="Date" DataFormatString="{0:dd/MM/yyyy HH:mm}" ItemStyle-Width="120px" />
                                    <asp:TemplateField HeaderText="Conversation" ItemStyle-Width="100px">
                                        <ItemTemplate>
                                            <asp:LinkButton ID="btnVoirConversation" runat="server" CommandName="voirconversation" 
                                                CommandArgument='<%# Eval("ConversationId") %>' CssClass="btn btn-info btn-sm">
                                                <i class="notika-icon notika-eye"></i> Voir
                                            </asp:LinkButton>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="Actions" ItemStyle-Width="150px">
                                        <ItemTemplate>
                                            <asp:LinkButton ID="btnSupprimer" runat="server" CommandName="supprimer" 
                                                CommandArgument='<%# Eval("id") %>' CssClass="btn btn-danger btn-sm"
                                                OnClientClick="return confirm('Êtes-vous sûr de vouloir supprimer ce message ?');">
                                                <i class="notika-icon notika-trash"></i> Supprimer
                                            </asp:LinkButton>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                                <PagerStyle CssClass="pagination-ys" />
                            </asp:GridView>
                        </div>

                        <!-- Statistiques -->
                        <div class="row" style="margin-top: 20px;">
                            <div class="col-lg-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h3 class="panel-title">Statistiques des Messages</h3>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-lg-3 col-md-6">
                                                <div class="statistic-box">
                                                    <h4>Total Messages</h4>
                                                    <h2><asp:Literal ID="litTotalMessages" runat="server">0</asp:Literal></h2>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <div class="statistic-box">
                                                    <h4>Messages Aujourd'hui</h4>
                                                    <h2><asp:Literal ID="litMessagesAujourdhui" runat="server">0</asp:Literal></h2>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <div class="statistic-box">
                                                    <h4>Conversations Actives</h4>
                                                    <h2><asp:Literal ID="litConversationsActives" runat="server">0</asp:Literal></h2>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <div class="statistic-box">
                                                    <h4>Utilisateurs Actifs</h4>
                                                    <h2><asp:Literal ID="litUtilisateursActifs" runat="server">0</asp:Literal></h2>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Data Table area End-->

    <style>
        .statistic-box {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .statistic-box h4 {
            color: #666;
            margin-bottom: 10px;
        }
        .statistic-box h2 {
            color: #333;
            font-weight: bold;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .alert {
            margin-bottom: 20px;
        }
    </style>
</asp:Content>
