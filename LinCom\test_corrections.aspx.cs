using LinCom.Imp;
using LinCom.Model;
using System;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class test_corrections : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Initialisation
            }
        }

        protected void btnRunTests_Click(object sender, EventArgs e)
        {
            StringBuilder results = new StringBuilder();
            bool allTestsPassed = true;

            try
            {
                // Test 1: Vérification des contrôles de recherche
                results.AppendLine("<div class='alert alert-info'><h5>🔍 Test 1: Contrôles de Recherche</h5>");
                
                try
                {
                    // Vérifier que les contrôles existent dans la page messagerie
                    var messagerieType = typeof(messagerie);
                    var txtRechercheField = messagerieType.GetField("txtRechercheContact", BindingFlags.NonPublic | BindingFlags.Instance);
                    var btnRechercherField = messagerieType.GetField("btnRechercherContact", BindingFlags.NonPublic | BindingFlags.Instance);
                    
                    if (txtRechercheField != null && btnRechercherField != null)
                    {
                        results.AppendLine("✅ Contrôles txtRechercheContact et btnRechercherContact trouvés<br/>");
                        lblTestControls.Text = "✅ Réussi";
                        lblTestControls.CssClass = "text-success";
                    }
                    else
                    {
                        results.AppendLine("❌ Contrôles manquants dans messagerie.aspx<br/>");
                        lblTestControls.Text = "❌ Échec";
                        lblTestControls.CssClass = "text-danger";
                        allTestsPassed = false;
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur lors de la vérification des contrôles: {ex.Message}<br/>");
                    lblTestControls.Text = "❌ Erreur";
                    lblTestControls.CssClass = "text-danger";
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 2: Vérification du modèle MessageStatu
                results.AppendLine("<div class='alert alert-info'><h5>📊 Test 2: Modèle MessageStatu</h5>");
                
                try
                {
                    var messageStatuType = typeof(MessageStatu);
                    var messageIdProp = messageStatuType.GetProperty("MessageId");
                    var userIdProp = messageStatuType.GetProperty("UserId");
                    var isReadProp = messageStatuType.GetProperty("IsRead");
                    var readAtProp = messageStatuType.GetProperty("ReadAt");
                    
                    if (messageIdProp != null && userIdProp != null && isReadProp != null && readAtProp != null)
                    {
                        results.AppendLine("✅ Toutes les propriétés MessageStatu sont présentes<br/>");
                        results.AppendLine($"📋 MessageId: {messageIdProp.PropertyType.Name}<br/>");
                        results.AppendLine($"📋 UserId: {userIdProp.PropertyType.Name}<br/>");
                        results.AppendLine($"📋 IsRead: {isReadProp.PropertyType.Name}<br/>");
                        results.AppendLine($"📋 ReadAt: {readAtProp.PropertyType.Name}<br/>");
                        
                        lblTestMessageStatu.Text = "✅ Réussi";
                        lblTestMessageStatu.CssClass = "text-success";
                    }
                    else
                    {
                        results.AppendLine("❌ Propriétés manquantes dans MessageStatu<br/>");
                        lblTestMessageStatu.Text = "❌ Échec";
                        lblTestMessageStatu.CssClass = "text-danger";
                        allTestsPassed = false;
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur lors de la vérification du modèle: {ex.Message}<br/>");
                    lblTestMessageStatu.Text = "❌ Erreur";
                    lblTestMessageStatu.CssClass = "text-danger";
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 3: Test de la méthode MarquerConversationCommeLue
                results.AppendLine("<div class='alert alert-info'><h5>✅ Test 3: Méthode MarquerConversationCommeLue</h5>");
                
                try
                {
                    var messageImp = new MessageImp();
                    var method = typeof(MessageImp).GetMethod("MarquerConversationCommeLue");
                    
                    if (method != null)
                    {
                        results.AppendLine("✅ Méthode MarquerConversationCommeLue trouvée<br/>");
                        var parameters = method.GetParameters();
                        results.AppendLine($"📋 Paramètres: {string.Join(", ", parameters.Select(p => p.ParameterType.Name + " " + p.Name))}<br/>");
                        
                        // Test avec des valeurs fictives (ne pas exécuter réellement)
                        results.AppendLine("✅ Signature de méthode correcte<br/>");
                        
                        lblTestMarquerLu.Text = "✅ Réussi";
                        lblTestMarquerLu.CssClass = "text-success";
                    }
                    else
                    {
                        results.AppendLine("❌ Méthode MarquerConversationCommeLue non trouvée<br/>");
                        lblTestMarquerLu.Text = "❌ Échec";
                        lblTestMarquerLu.CssClass = "text-danger";
                        allTestsPassed = false;
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur lors du test de la méthode: {ex.Message}<br/>");
                    lblTestMarquerLu.Text = "❌ Erreur";
                    lblTestMarquerLu.CssClass = "text-danger";
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 4: Vérification du compteur de messages
                results.AppendLine("<div class='alert alert-info'><h5>🔔 Test 4: Compteur Messages Non Lus</h5>");
                
                try
                {
                    var pageMasterType = typeof(PageMaster);
                    var litMessagesNonLusField = pageMasterType.GetField("litMessagesNonLus", BindingFlags.NonPublic | BindingFlags.Instance);
                    
                    if (litMessagesNonLusField != null)
                    {
                        results.AppendLine("✅ Contrôle litMessagesNonLus trouvé dans PageMaster<br/>");
                        results.AppendLine($"📋 Type: {litMessagesNonLusField.FieldType.Name}<br/>");
                        
                        lblTestCompteur.Text = "✅ Réussi";
                        lblTestCompteur.CssClass = "text-success";
                    }
                    else
                    {
                        results.AppendLine("❌ Contrôle litMessagesNonLus non trouvé<br/>");
                        lblTestCompteur.Text = "❌ Échec";
                        lblTestCompteur.CssClass = "text-danger";
                        allTestsPassed = false;
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ Erreur lors de la vérification du compteur: {ex.Message}<br/>");
                    lblTestCompteur.Text = "❌ Erreur";
                    lblTestCompteur.CssClass = "text-danger";
                    allTestsPassed = false;
                }
                results.AppendLine("</div>");

                // Test 5: Test de connexion à la base de données
                results.AppendLine("<div class='alert alert-info'><h5>🗄️ Test 5: Base de Données</h5>");
                
                try
                {
                    using (Connection con = new Connection())
                    {
                        // Test simple de connexion
                        var messageStatusCount = con.MessageStatus.Count();
                        results.AppendLine($"✅ Connexion à la base de données réussie<br/>");
                        results.AppendLine($"📊 {messageStatusCount} enregistrements MessageStatus trouvés<br/>");
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"⚠️ Connexion DB: {ex.Message}<br/>");
                }
                results.AppendLine("</div>");

                // Résumé final
                if (allTestsPassed)
                {
                    results.AppendLine("<div class='alert alert-success'><h4>🎉 Toutes les corrections sont validées !</h4>");
                    results.AppendLine("Le module de messagerie est prêt pour la compilation et l'utilisation.</div>");
                }
                else
                {
                    results.AppendLine("<div class='alert alert-warning'><h4>⚠️ Certaines corrections nécessitent une attention</h4>");
                    results.AppendLine("Veuillez vérifier les erreurs ci-dessus.</div>");
                }

            }
            catch (Exception ex)
            {
                results.AppendLine($"<div class='alert alert-danger'><h4>❌ Erreur générale</h4>{ex.Message}</div>");
            }

            litResults.Text = results.ToString();
            divResults.Visible = true;
        }
    }
}
